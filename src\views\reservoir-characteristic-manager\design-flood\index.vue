<!-- 设计洪水 -->
<template>
  <div class="design-storm-container">
    <!-- 左侧区域 -->
    <div class="left-section">
      <!-- 上半部分：桃花江水库设计洪水表 -->
      <div class="top-section">
        <div class="section-header">
          <div class="title-row">
            <h3 class="section-title">桃花江水库设计洪水表</h3>
          </div>
          <div class="header-row">
            <div class="unit-text">单位：m³/s</div>
            <a-button type="primary" size="small" icon="plus" @click="handleAdd">添加</a-button>
          </div>
        </div>
        <div class="table-container">
          <vxe-table
            ref="floodTableRef"
            :data="floodTableData"
            border
            size="small"
            :merge-cells="mergeCells"
            :column-config="{ resizable: true }"
            :auto-resize="true"
            :show-overflow="false"
            :stripe="false"
            height="300px"
            max-height="300px"
          >
            <vxe-column field="stage" title="阶段" width="21%"></vxe-column>
            <vxe-column field="peakVolume" title="峰量" width="12%"></vxe-column>
            <vxe-column field="p333" title="P=3.33%" width="17%"></vxe-column>
            <vxe-column field="p2" title="P=2%" width="17%"></vxe-column>
            <vxe-column field="p02" title="P=0.2%" width="17%"></vxe-column>
            <vxe-column title="操作" width="16%">
              <template #default="{ row }">
                <a-button type="link" size="small" @click="handleEdit(row)">编辑</a-button>
                <a-divider type="vertical" />
                <a-button type="link" size="small" danger @click="handleDelete(row)">删除</a-button>
              </template>
            </vxe-column>
          </vxe-table>
        </div>
      </div>

      <!-- 下半部分：桃花江水库各频率设计洪水过程表 -->
      <div class="bottom-section">
        <div class="section-header">
          <h3 class="section-title">桃花江水库各频率设计洪水过程表</h3>
        </div>
        <div class="table-container">
          <vxe-table
            ref="processTableRef"
            :data="processTableData"
            border
            size="small"
            height="auto"
            max-height="300px"
            :column-config="{ resizable: true }"
            :auto-resize="true"
          >
            <vxe-column field="timeSlot" title="时段（1h）" width="25%"></vxe-column>
            <vxe-column field="p333" title="P=3.33%" width="25%"></vxe-column>
            <vxe-column field="p2" title="P=2%" width="25%"></vxe-column>
            <vxe-column field="p02" title="P=0.2%" width="25%"></vxe-column>
          </vxe-table>
        </div>
      </div>
    </div>

    <!-- 右侧区域 -->
    <div class="right-section">
      <!-- 操作栏 -->
      <div class="operation-bar">
        <div>洪水过程线:</div>
        <a-select
          v-model="selectedStage"
          placeholder="请选择洪水过程"
          style="width: 200px; margin-right: 8px"
          @change="handleStageChange"
        >
          <a-select-option v-for="option in floodDurationOptions" :key="option.id" :value="option.id.toString()">
            {{ option.name }}
          </a-select-option>
        </a-select>
        <a-button type="default" icon="upload" @click="handleUpload">上传</a-button>
        <a-button type="default" icon="download" @click="handleDownload">下载</a-button>
        <a-button type="primary" @click="handleSetDefault">设为默认</a-button>
      </div>

      <!-- 图表区域 -->
      <div class="chart-container">
        <div class="chart-title">桃花江水库各频率设计洪水过程线</div>
        <div class="chart-content">
          <LineEchart :dataSource="chartData.dataSource" :custom="chartData.custom" height="100%" />
        </div>
      </div>
    </div>
    <!-- 新增/编辑弹窗 -->
    <FormDrawer
      v-if="showFormDrawer"
      ref="formDrawerRef"
      :floodDurationList="floodDurationOptions"
      @ok="onOperationComplete"
      @close="showFormDrawer = false"
    ></FormDrawer>
    <FormModal v-if="showFormModal" ref="formModalRef" @ok="onOperationComplete" @close="showFormModal = false" />
  </div>
</template>

<script>
  import FormModal from './modules/FormModal.vue'
  import FormDrawer from './modules/FormDrawer.vue'
  import moment from 'moment'
  import {
    getCstDesignFloodPage,
    getCstDesignFloodDurationPage,
    setDefaultCstDesignFloodDuration,
    delCstDesignFlood,
  } from './services'
  import { LineEchart } from '@/components/Echarts'
  import excelExport from '@/utils/excelExport.js'

  export default {
    name: 'DesignStorm',
    components: {
      LineEchart,
      FormModal,
      FormDrawer,
    },
    data() {
      return {
        isDefault: false,
        showFormDrawer: false,
        showFormModal: false,
        selectedStage: '',

        // 洪水过程选项数组
        floodDurationOptions: [],

        // 设计洪水表数据
        floodTableData: [
          { stage: '2024年', peakVolume: '洪峰', p333: 1017, p2: 661, p02: 605 },
          { stage: '2024年', peakVolume: '洪量', p333: 3974, p2: 2748, p02: 2470 },
          { stage: '2019年加固', peakVolume: '洪峰', p333: 1260, p2: 859, p02: 739 },
          { stage: '2019年加固', peakVolume: '洪量', p333: 6440, p2: 4240, p02: 3750 },
          { stage: '1966年初设', peakVolume: '洪峰', p333: 1260, p2: 859, p02: 739 },
          { stage: '1966年初设', peakVolume: '洪量', p333: 6440, p2: 4240, p02: 3750 },
        ],

        // VxeTable合并单元格配置
        mergeCells: [
          { row: 0, col: 0, rowspan: 2, colspan: 1 }, // 2024年
          { row: 2, col: 0, rowspan: 2, colspan: 1 }, // 2019年加固
          { row: 4, col: 0, rowspan: 2, colspan: 1 }, // 1966年初设
        ],

        // 洪水过程表数据
        processTableData: [],
      }
    },

    computed: {
      // 图表数据配置
      chartData() {
        return {
          dataSource: [
            {
              name: 'P=3.33%',
              color: '#1890ff',
              data: this.processTableData.map(item => [item.timeSlot, item.p333]),
            },
            {
              name: 'P=2%',
              color: '#52c41a',
              data: this.processTableData.map(item => [item.timeSlot, item.p2]),
            },
            {
              name: 'P=0.2%',
              color: '#fa8c16',
              data: this.processTableData.map(item => [item.timeSlot, item.p02]),
            },
          ],
          custom: {
            xLabel: 'T(h)',
            yLabel: 'Q（m³/s）',
            legend: true,
            legendOptions: {
              top: 30,
              left: 'center',
            },
            showAreaStyle: false,
            dataZoom: true,
            grid: {
              top: 80,
              left: 60,
              right: 40,
              bottom: 80,
            },
          },
        }
      },
    },
    created() {
      this.getList()
    },
    methods: {
      getList() {
        const param = {
          pageNum: 1,
          pageSize: Number.MAX_SAFE_INTEGER,
        }
        getCstDesignFloodPage(param).then(res => {
          if (res.code == 200) {
            // 将接口数据转换为floodTableData格式
            const transformedData = []
            res.data?.data?.forEach(item => {
              const stage = item.period
              const id = item.id
              // 创建洪峰和洪量对象，保留id字段
              const floodPeak = { id: id, stage: stage, peakVolume: '洪峰', p333: null, p2: null, p02: null }
              const floodVolume = { id: id, stage: stage, peakVolume: '洪量', p333: null, p2: null, p02: null }

              // 处理每个频率
              item.frequencyList.forEach(freq => {
                const frequency = freq.frequency
                // 处理每个值
                freq.values.forEach(value => {
                  const charType = value.charType
                  const floodValue = value.floodValue

                  // 根据频率和类型设置对应的值
                  if (frequency === 3.33) {
                    charType === 1 ? (floodPeak.p333 = floodValue) : (floodVolume.p333 = floodValue)
                  } else if (frequency === 2) {
                    charType === 1 ? (floodPeak.p2 = floodValue) : (floodVolume.p2 = floodValue)
                  } else if (frequency === 0.2) {
                    charType === 1 ? (floodPeak.p02 = floodValue) : (floodVolume.p02 = floodValue)
                  }
                })
              })

              // 添加到结果数组
              transformedData.push(floodPeak)
              transformedData.push(floodVolume)
            })

            this.floodTableData = transformedData
            // 重新计算合并单元格
            this.calculateMergeCells()
          }
        })
        getCstDesignFloodDurationPage(param).then(res => {
          if (res.code == 200) {
            const data = res.data?.data || []

            // 设置洪水过程选项
            this.floodDurationOptions = data.map(item => ({
              id: item.id,
              name: item.durationName,
              isDefault: item.isDefault,
            }))

            // 默认选中第一个默认项
            if (this.floodDurationOptions.length > 0) {
              this.selectedStage =
                this.floodDurationOptions.find(item => item.isDefault == 1)?.id.toString() ||
                this.floodDurationOptions[0].id.toString()
              // 加载默认选项的数据
              this.updateProcessTableData(this.selectedStage, data)
            }
          }
          console.log('获取洪水过程列表', res)
        })
      },
      // 阶段选择变化
      handleStageChange(value) {
        // 根据选择的id重新获取并更新洪水过程数据
        const param = {
          pageNum: 1,
          pageSize: Number.MAX_SAFE_INTEGER,
        }
        getCstDesignFloodDurationPage(param).then(res => {
          if (res.code == 200) {
            const data = res.data?.data || []
            this.updateProcessTableData(value, data)
          }
        })
      },

      // 根据选中的洪水过程id更新processTableData
      updateProcessTableData(selectedId, data) {
        // 查找选中的数据项
        const selectedItem = data.find(item => item.id == selectedId)
        if (!selectedItem) {
          this.processTableData = []
          return
        }

        // 将接口数据转换为processTableData格式
        const processTableData = []
        const frequencyList = selectedItem.frequencyList || []

        // 创建一个映射，用于按durationHour存储不同频率的floodValue
        const floodValuesMap = new Map()

        // 遍历所有频率
        frequencyList.forEach(freq => {
          const frequency = freq.frequency
          const values = freq.values || []

          // 遍历每个频率的values
          values.forEach(value => {
            const durationHour = value.durationHour
            const floodValue = value.floodValue

            // 如果该durationHour不存在，则创建一个新对象
            if (!floodValuesMap.has(durationHour)) {
              floodValuesMap.set(durationHour, {
                timeSlot: durationHour,
                p333: null,
                p2: null,
                p02: null,
              })
            }

            // 根据频率设置对应的floodValue
            const item = floodValuesMap.get(durationHour)
            if (frequency === 3.33) {
              item.p333 = floodValue
            } else if (frequency === 2) {
              item.p2 = floodValue
            } else if (frequency === 0.2) {
              item.p02 = floodValue
            }
          })
        })

        // 将映射转换为数组，并添加key
        Array.from(floodValuesMap.entries())
          .sort(([hour1], [hour2]) => hour1 - hour2) // 按timeSlot排序
          .forEach(([_, item], index) => {
            processTableData.push({
              key: (index + 1).toString(),
              ...item,
            })
          })

        this.processTableData = processTableData
      },

      // 添加数据
      handleAdd() {
        // this.$message.info('添加功能待实现')
        this.showFormModal = true
        this.$nextTick(() => {
          this.$refs.formModalRef.add()
        })
      },

      // 编辑数据
      handleEdit(record) {
        // this.$message.info(`编辑数据: ${record.stage}`)
        this.showFormModal = true
        this.$nextTick(() => {
          this.$refs.formModalRef.edit(record)
        })
      },

      // 删除数据
      handleDelete(record) {
        const that = this
        this.$confirm({
          title: '确认删除',
          content: `确认删除阶段"${record.stage}"的设计暴雨数据吗？`,
          onOk() {
            return delCstDesignFlood({ id: record.id })
              .then(res => {
                that.$message.success(`成功删除1条数据`, 3)
                that.onOperationComplete()
                that.getList()
              })
              .catch(err => {
                that.$message.error('删除失败，请重试')
                console.error('删除操作失败:', err)
              })
          },
        })
        // this.$confirm({
        //   title: '确认删除',
        //   content: `确定要删除 ${record.stage} 的数据吗？`,
        //   onOk: () => {
        //     this.$message.success('删除成功')
        //   },
        // })
      },

      // 上传文件
      handleUpload() {
        this.showFormDrawer = true
        this.$nextTick(() => {
          this.$refs.formDrawerRef.add()
        })
        // this.$message.info('上传功能待实现')
      },

      // 下载文件
      handleDownload() {
        // 获取当前选中的洪水过程名称
        const selectedItem = this.floodDurationOptions.find(option => option.id.toString() === this.selectedStage)
        const fileName = selectedItem ? `${selectedItem.name}设计洪水过程表` : '桃花江水库设计洪水过程表'

        const columnsList = [
          {
            title: '时段（1h）',
            field: 'timeSlot',
            minWidth: 100,
          },
          {
            title: 'P=3.33%',
            field: 'p333',
            minWidth: 100,
          },
          {
            title: 'P=2%',
            field: 'p2',
            minWidth: 100,
          },
          {
            title: 'P=0.2%',
            field: 'p02',
            minWidth: 100,
          },
        ]

        // 导出Excel this.processTableData
        // excelExport(exportData, fileName, ['timeSlot', 'p333', 'p2', 'p02'])
        excelExport(columnsList, this.processTableData, `${fileName}${moment().format('YYYYMMDDHHmmss')}`)
      },

      // 设为默认
      handleSetDefault() {
        if (this.selectedStage == '') {
          this.$message.error(`请选择曲线`, 3)
          return
        }
        if (this.isDefault) {
          this.$message.error(`该曲线已是默认曲线`, 3)
          return
        }

        setDefaultCstDesignFloodDuration({ floodDurationId: this.selectedStage }).then(res => {
          this.isDefault = true
          this.$message.success(`成功设置默认数据`, 3)
        })
        // this.$message.success('已设为默认')
      },

      // 计算合并单元格
      calculateMergeCells() {
        const mergeCells = []
        const stageMap = new Map()

        // 按阶段分组并记录起始行索引
        this.floodTableData.forEach((item, index) => {
          if (!stageMap.has(item.stage)) {
            stageMap.set(item.stage, index)
          }
        })

        // 为每个阶段创建合并单元格配置
        stageMap.forEach((startIndex, stage) => {
          mergeCells.push({
            row: startIndex,
            col: 0,
            rowspan: 2,
            colspan: 1,
          })
        })

        this.mergeCells = mergeCells
      },
      // 操作完成后刷新列表
      onOperationComplete() {
        this.getList()
      },
    },
  }
</script>

<style lang="less" scoped>
  .design-storm-container {
    height: 100vh;
    display: flex;
    gap: 16px;
    padding: 16px;
    background-color: #fff;
    box-sizing: border-box;

    .left-section {
      flex: 1;
      display: flex;
      flex-direction: column;
      gap: 8px;

      .top-section {
        background: white;
        border-radius: 6px;
        padding: 16px;
        display: flex;
        flex-direction: column;
        //   box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        .section-header {
          width: 100%;
          margin-bottom: 16px;

          .title-row {
            margin-bottom: 8px;
          }

          .section-title {
            margin: 0;
            font-size: 16px;
            font-weight: 600;
            color: #262626;
          }

          .header-row {
            width: 100%;
            display: flex;
            justify-content: space-between;
            align-items: center;

            .unit-text {
              color: #666;
              font-size: 14px;
              flex-shrink: 0;
            }
          }
        }
      }

      .bottom-section {
        flex: 1;
        background: white;
        border-radius: 6px;
        padding: 16px;
        display: flex;
        flex-direction: column;
        min-height: 0;
        //   box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

        .section-header {
          width: 100%;
          margin-bottom: 16px;

          .title-row {
            margin-bottom: 8px;
          }

          .section-title {
            margin: 0;
            font-size: 16px;
            font-weight: 600;
            color: #262626;
          }

          .header-row {
            width: 100%;
            display: flex;
            justify-content: space-between;
            align-items: center;

            .unit-text {
              color: #666;
              font-size: 14px;
              flex-shrink: 0;
            }
          }
        }

        .table-container {
          flex: 1;
          overflow: hidden;
        }
      }
    }

    .right-section {
      flex: 1;
      background: white;
      border-radius: 6px;
      padding: 16px;
      // box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      display: flex;
      flex-direction: column;

      .operation-bar {
        display: flex;
        align-items: center;
        margin-bottom: 16px;
        gap: 8px;
      }

      .chart-container {
        flex: 1;
        min-height: 400px;
        display: flex;
        flex-direction: column;

        .chart-title {
          font-size: 16px;
          font-weight: 600;
          color: #262626;
          text-align: center;
          margin-bottom: 16px;
          padding: 8px 0;
        }

        .chart-content {
          flex: 1;
          min-height: 350px;
        }
      }
    }
  }

  // 隐藏VxeTable的空行
  ::v-deep .vxe-table--render-default {
    .vxe-body--row:empty {
      display: none;
    }

    .vxe-body--row {
      &:last-child {
        .vxe-body--column:empty {
          display: none;
        }
      }
    }
  }

  // 确保表格高度适应内容
  ::v-deep .vxe-table--body-wrapper {
    overflow: hidden !important;
  }
</style>
